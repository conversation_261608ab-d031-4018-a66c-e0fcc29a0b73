#!/usr/bin/env python3
"""
Test script for symbol loading and validation.
Tests the enhanced symbol service and universal symbol parser integration.
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

import logging
from database.connection import get_db, check_database_connection
from services.enhanced_symbol_service import EnhancedSymbolService
from database.models import MarketType
from core.logging import setup_enhanced_logging

# Setup logging
setup_enhanced_logging()
logger = logging.getLogger(__name__)


def test_symbol_download_and_population():
    """Test symbol download and population functionality."""
    logger.info("🧪 Testing symbol download and population...")
    
    try:
        # Check database connection
        if not check_database_connection():
            logger.error("❌ Database connection failed")
            return False
        
        # Get database session
        db = next(get_db())
        
        try:
            # Initialize enhanced symbol service
            symbol_service = EnhancedSymbolService(db)
            
            # Test symbol download and population
            logger.info("📥 Starting symbol download and population test...")
            results = symbol_service.fetch_and_populate_all_symbols(force_refresh=True)
            
            # Display results
            logger.info("📊 Symbol Download Results:")
            logger.info(f"  Equity symbols: {results.get('equity_count', 0):,}")
            logger.info(f"  Index symbols: {results.get('index_count', 0):,}")
            logger.info(f"  Futures symbols: {results.get('futures_count', 0):,}")
            logger.info(f"  Options symbols: {results.get('options_count', 0):,}")
            logger.info(f"  Total symbols: {results.get('total_count', 0):,}")
            
            # Validate data integrity
            logger.info("\n🔍 Validating data integrity...")
            integrity_results = symbol_service.validate_symbol_data_integrity()
            logger.info(f"  Total symbols in DB: {integrity_results.get('total_symbols', 0):,}")
            logger.info(f"  Total mappings in DB: {integrity_results.get('total_mappings', 0):,}")
            logger.info(f"  Orphaned mappings: {integrity_results.get('orphaned_mappings', 0):,}")
            logger.info(f"  Missing mappings: {integrity_results.get('missing_mappings', 0):,}")
            
            # Get market type summary
            logger.info("\n📈 Market Type Summary:")
            summary = symbol_service.get_market_type_summary()
            for market_type, data in summary.items():
                logger.info(f"  {market_type}: {data['symbol_count']:,} symbols, {data['mapping_count']:,} mappings")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in symbol download test: {e}")
        return False


def test_symbol_retrieval_by_market_type():
    """Test symbol retrieval by market type."""
    logger.info("\n🧪 Testing symbol retrieval by market type...")
    
    try:
        db = next(get_db())
        
        try:
            symbol_service = EnhancedSymbolService(db)
            
            # Test each market type
            for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                logger.info(f"\n📋 Testing {market_type.value} symbols...")
                
                # Get symbols without filter
                symbols = symbol_service.get_symbols_by_market_type(market_type, limit=10)
                logger.info(f"  Retrieved {len(symbols)} {market_type.value} symbols (limit 10)")
                
                # Display first few symbols
                for i, symbol in enumerate(symbols[:5]):
                    logger.info(f"    {i+1}. {symbol['symbol']} - {symbol['name']}")
                
                # Test with underlying filter for specific market types
                if market_type in [MarketType.EQUITY, MarketType.FUTURES, MarketType.OPTIONS]:
                    filtered_symbols = symbol_service.get_symbols_by_market_type(
                        market_type, 
                        underlying_filter=['RELIANCE', 'TCS', 'INFY'],
                        limit=5
                    )
                    logger.info(f"  Filtered symbols (RELIANCE/TCS/INFY): {len(filtered_symbols)}")
                
                elif market_type == MarketType.INDEX:
                    filtered_symbols = symbol_service.get_symbols_by_market_type(
                        market_type,
                        underlying_filter=['NIFTY', 'BANKNIFTY'],
                        limit=5
                    )
                    logger.info(f"  Filtered symbols (NIFTY/BANKNIFTY): {len(filtered_symbols)}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in symbol retrieval test: {e}")
        return False


def test_symbol_mapping_functionality():
    """Test symbol mapping functionality."""
    logger.info("\n🧪 Testing symbol mapping functionality...")
    
    try:
        db = next(get_db())
        
        try:
            symbol_service = EnhancedSymbolService(db)
            
            # Test symbol mappings for different market types
            test_symbols = [
                ('RELIANCE-EQ', MarketType.EQUITY),
                ('NIFTY50-INDEX', MarketType.INDEX),
                ('TCS', MarketType.EQUITY),  # Test without suffix
            ]
            
            for nse_symbol, market_type in test_symbols:
                logger.info(f"\n🔍 Testing mapping for {nse_symbol} ({market_type.value})...")
                
                mapping = symbol_service.get_symbol_mapping(nse_symbol, market_type)
                if mapping:
                    logger.info(f"  ✅ Found mapping:")
                    logger.info(f"    NSE Symbol: {mapping['nse_symbol']}")
                    logger.info(f"    Fyers Symbol: {mapping['fyers_symbol']}")
                    logger.info(f"    Market Type: {mapping['market_type']}")
                    if mapping['expiry_date']:
                        logger.info(f"    Expiry Date: {mapping['expiry_date']}")
                    if mapping['strike_price']:
                        logger.info(f"    Strike Price: {mapping['strike_price']}")
                    if mapping['option_type']:
                        logger.info(f"    Option Type: {mapping['option_type']}")
                else:
                    logger.warning(f"  ⚠️  No mapping found for {nse_symbol}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in symbol mapping test: {e}")
        return False


def test_symbols_for_data_loading():
    """Test getting symbols ready for data loading."""
    logger.info("\n🧪 Testing symbols for data loading...")
    
    try:
        db = next(get_db())
        
        try:
            # Test with specific target symbols
            target_symbols = ['RELIANCE', 'TCS', 'INFY', 'NIFTY', 'BANKNIFTY']
            symbol_service = EnhancedSymbolService(db, target_symbols=target_symbols)
            
            # Test each market type
            for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                logger.info(f"\n📊 Getting {market_type.value} symbols for data loading...")
                
                symbols = symbol_service.get_symbols_for_data_loading(
                    market_type=market_type,
                    target_symbols=target_symbols[:3],  # Use first 3 symbols
                    limit=10
                )
                
                logger.info(f"  Found {len(symbols)} symbols ready for data loading")
                for i, symbol in enumerate(symbols[:5]):
                    logger.info(f"    {i+1}. {symbol}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Error in data loading symbols test: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🚀 Starting Symbol Loading Tests")
    logger.info("=" * 80)
    
    tests = [
        ("Symbol Download and Population", test_symbol_download_and_population),
        ("Symbol Retrieval by Market Type", test_symbol_retrieval_by_market_type),
        ("Symbol Mapping Functionality", test_symbol_mapping_functionality),
        ("Symbols for Data Loading", test_symbols_for_data_loading),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            failed += 1
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Total tests: {passed + failed}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {(passed/(passed+failed)*100):.1f}%" if (passed+failed) > 0 else "N/A")
    
    return failed == 0


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
