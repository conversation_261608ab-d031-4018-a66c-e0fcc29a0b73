#!/usr/bin/env python3
"""
Simple test script for symbol loading functionality.
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_symbol_download():
    """Test symbol download functionality."""
    print("Testing Enhanced Symbol Service...")
    
    try:
        from database.connection import get_db
        from services.enhanced_symbol_service import EnhancedSymbolService
        
        # Get database session
        db = next(get_db())
        
        try:
            # Initialize enhanced symbol service
            symbol_service = EnhancedSymbolService(db, target_symbols=['RELIANCE', 'TCS', 'NIFTY'])
            print("Enhanced symbol service initialized")
            
            # Test symbol download and population
            print("Testing symbol download and population...")
            results = symbol_service.fetch_and_populate_all_symbols(force_refresh=True)
            
            print("Symbol Download Results:")
            print(f"  Equity symbols: {results.get('equity_count', 0):,}")
            print(f"  Index symbols: {results.get('index_count', 0):,}")
            print(f"  Futures symbols: {results.get('futures_count', 0):,}")
            print(f"  Options symbols: {results.get('options_count', 0):,}")
            print(f"  Total symbols: {results.get('total_count', 0):,}")
            
            print("Symbol download test completed successfully")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Error in symbol download test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_market_type_summary():
    """Test market type summary functionality."""
    print("\nTesting market type summary...")
    
    try:
        from database.connection import get_db
        from services.enhanced_symbol_service import EnhancedSymbolService
        
        db = next(get_db())
        
        try:
            symbol_service = EnhancedSymbolService(db)
            
            # Get market type summary
            summary = symbol_service.get_market_type_summary()
            print("Market Type Summary:")
            for market_type, data in summary.items():
                print(f"  {market_type}: {data['symbol_count']:,} symbols, {data['mapping_count']:,} mappings")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Error in market type summary test: {e}")
        return False

def main():
    """Main test function."""
    print("Starting Symbol Loading Tests")
    print("=" * 50)
    
    tests = [
        ("Symbol Download", test_symbol_download),
        ("Market Type Summary", test_market_type_summary),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name} Test:")
        print("-" * 30)
        try:
            if test_func():
                print(f"PASSED: {test_name}")
                passed += 1
            else:
                print(f"FAILED: {test_name}")
                failed += 1
        except Exception as e:
            print(f"FAILED: {test_name} with exception: {e}")
            failed += 1
    
    # Final summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Total tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    if (passed + failed) > 0:
        print(f"Success rate: {(passed/(passed+failed)*100):.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
