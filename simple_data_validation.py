#!/usr/bin/env python3
"""
Simple Data Validation Script.
Validates data integrity without importing models that cause conflicts.
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def validate_database_data():
    """Validate database data using direct SQL queries."""
    print("🔍 Validating database data...")
    
    try:
        from database.connection import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        try:
            # Check symbol tables
            print("\n📊 Symbol Tables Analysis:")
            
            # Check symbols table
            result = db.execute(text("SELECT COUNT(*) FROM symbols"))
            symbols_count = result.scalar()
            print(f"  Symbols table: {symbols_count:,} records")
            
            # Check symbol_mapping table
            result = db.execute(text("SELECT COUNT(*) FROM symbol_mapping"))
            mappings_count = result.scalar()
            print(f"  Symbol mappings table: {mappings_count:,} records")
            
            # Check market type distribution in symbols
            result = db.execute(text("""
                SELECT market_type, COUNT(*) as count 
                FROM symbols 
                GROUP BY market_type 
                ORDER BY count DESC
            """))
            
            print("  Market type distribution in symbols:")
            for market_type, count in result.fetchall():
                print(f"    {market_type}: {count:,}")
            
            # Check market type distribution in mappings
            result = db.execute(text("""
                SELECT market_type, COUNT(*) as count 
                FROM symbol_mapping 
                GROUP BY market_type 
                ORDER BY count DESC
            """))
            
            print("  Market type distribution in mappings:")
            for market_type, count in result.fetchall():
                print(f"    {market_type}: {count:,}")
            
            # Check OHLCV data tables
            print("\n📊 OHLCV Data Tables Analysis:")
            
            tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
            total_records = 0
            
            for table in tables:
                try:
                    result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    total_records += count
                    print(f"  {table}: {count:,} records")
                    
                    if count > 0:
                        # Get unique symbols
                        result = db.execute(text(f"SELECT COUNT(DISTINCT symbol) FROM {table}"))
                        unique_symbols = result.scalar()
                        print(f"    Unique symbols: {unique_symbols}")
                        
                        # Get date range
                        result = db.execute(text(f"SELECT MIN(datetime), MAX(datetime) FROM {table}"))
                        min_date, max_date = result.fetchone()
                        print(f"    Date range: {min_date} to {max_date}")
                        
                except Exception as e:
                    print(f"    Error checking {table}: {e}")
            
            print(f"\n📈 Total OHLCV records: {total_records:,}")
            
            # Check for data integrity issues
            print("\n🔍 Data Integrity Checks:")
            
            # Check for orphaned mappings
            result = db.execute(text("""
                SELECT COUNT(*) 
                FROM symbol_mapping sm 
                WHERE sm.nse_symbol NOT IN (SELECT symbol FROM symbols)
            """))
            orphaned_mappings = result.scalar()
            print(f"  Orphaned mappings: {orphaned_mappings}")
            
            # Check for symbols without mappings
            result = db.execute(text("""
                SELECT COUNT(*) 
                FROM symbols s 
                WHERE s.symbol NOT IN (SELECT nse_symbol FROM symbol_mapping)
            """))
            missing_mappings = result.scalar()
            print(f"  Symbols without mappings: {missing_mappings}")
            
            # Sample some symbols and their mappings
            print("\n📋 Sample Symbol Mappings:")
            result = db.execute(text("""
                SELECT s.symbol, s.market_type, sm.fyers_symbol 
                FROM symbols s 
                LEFT JOIN symbol_mapping sm ON s.symbol = sm.nse_symbol 
                LIMIT 10
            """))
            
            for symbol, market_type, fyers_symbol in result.fetchall():
                mapping_status = "✅" if fyers_symbol else "❌"
                print(f"  {mapping_status} {symbol} ({market_type}) -> {fyers_symbol or 'No mapping'}")
            
            # Check if equity data symbols have mappings
            print("\n🔍 Equity Data vs Symbol Mappings:")
            result = db.execute(text("""
                SELECT 
                    COUNT(DISTINCT e.symbol) as total_equity_symbols,
                    COUNT(DISTINCT sm.nse_symbol) as mapped_equity_symbols
                FROM equity_ohlcv e
                LEFT JOIN symbol_mapping sm ON e.symbol = sm.nse_symbol AND sm.market_type = 'EQUITY'
            """))
            
            total_equity, mapped_equity = result.fetchone()
            print(f"  Equity symbols in OHLCV: {total_equity}")
            print(f"  Equity symbols with mappings: {mapped_equity}")
            print(f"  Unmapped equity symbols: {total_equity - mapped_equity}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error in database validation: {e}")
        import traceback
        traceback.print_exc()
        return False


def validate_symbol_download_results():
    """Validate the results of symbol download process."""
    print("\n🔍 Validating symbol download results...")
    
    try:
        from database.connection import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        try:
            # Check if we have symbols from both CSV files
            print("📊 Symbol Source Analysis:")
            
            # Count symbols that look like they came from NSE_CM (equity/index patterns)
            result = db.execute(text("""
                SELECT COUNT(*) 
                FROM symbols 
                WHERE market_type IN ('EQUITY', 'INDEX')
            """))
            cm_symbols = result.scalar()
            print(f"  NSE_CM symbols (EQUITY/INDEX): {cm_symbols:,}")
            
            # Count symbols that look like they came from NSE_FO (futures/options patterns)
            result = db.execute(text("""
                SELECT COUNT(*) 
                FROM symbols 
                WHERE market_type IN ('FUTURES', 'OPTIONS')
            """))
            fo_symbols = result.scalar()
            print(f"  NSE_FO symbols (FUTURES/OPTIONS): {fo_symbols:,}")
            
            # Check for specific target symbols we were looking for
            target_symbols = ['RELIANCE', 'TCS', 'NIFTY']
            print(f"\n🎯 Target Symbols Analysis:")
            
            for target in target_symbols:
                result = db.execute(text("""
                    SELECT symbol, market_type 
                    FROM symbols 
                    WHERE symbol LIKE :pattern
                    ORDER BY symbol
                """), {"pattern": f"%{target}%"})
                
                matches = result.fetchall()
                print(f"  {target}: {len(matches)} matches")
                for symbol, market_type in matches[:5]:  # Show first 5
                    print(f"    - {symbol} ({market_type})")
                if len(matches) > 5:
                    print(f"    ... and {len(matches) - 5} more")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error in symbol download validation: {e}")
        return False


def main():
    """Main validation function."""
    print("🚀 Starting Simple Data Validation")
    print("=" * 60)
    
    validations = [
        ("Database Data", validate_database_data),
        ("Symbol Download Results", validate_symbol_download_results),
    ]
    
    passed = 0
    failed = 0
    
    for validation_name, validation_func in validations:
        print(f"\n{'='*15} {validation_name} {'='*15}")
        try:
            if validation_func():
                print(f"✅ {validation_name} PASSED")
                passed += 1
            else:
                print(f"❌ {validation_name} FAILED")
                failed += 1
        except Exception as e:
            print(f"❌ {validation_name} FAILED with exception: {e}")
            failed += 1
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Total validations: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    if (passed + failed) > 0:
        print(f"Success rate: {(passed/(passed+failed)*100):.1f}%")
    
    return failed == 0


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
